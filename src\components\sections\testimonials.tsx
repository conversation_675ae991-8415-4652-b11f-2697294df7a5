'use client'

import Image from 'next/image'
import { <PERSON>, Quote, Loader2, AlertCircle } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Section, SectionHeader } from '@/components/ui/section'
import { AnimatedElement, StaggeredContainer, StaggeredItem } from '@/components/ui/animated-element'
import { useApprovedTestimonials } from '@/hooks/use-api'
import { formatDate } from '@/lib/utils'
import { Button } from '@/components/ui/button'

export default function Testimonials() {
  const { data: testimonialsData, isLoading, error } = useApprovedTestimonials(6)

  const testimonials = testimonialsData?.testimonials || []

  // Loading state
  if (isLoading) {
    return (
      <Section background="gradient" id="testimonials">
        <AnimatedElement animation="fadeIn">
          <SectionHeader
            subtitle="Client Reviews"
            title="What Our Clients Say"
            description="Read what our satisfied clients have to say about their experience with our professional makeup services."
          />
        </AnimatedElement>
        <div className="flex justify-center items-center py-20">
          <Loader2 className="w-8 h-8 animate-spin text-rose-gold" />
          <span className="ml-3 text-white/80">Loading testimonials...</span>
        </div>
      </Section>
    )
  }

  // Error state
  if (error) {
    return (
      <Section background="gradient" id="testimonials">
        <AnimatedElement animation="fadeIn">
          <SectionHeader
            subtitle="Client Reviews"
            title="What Our Clients Say"
            description="Read what our satisfied clients have to say about their experience with our professional makeup services."
          />
        </AnimatedElement>
        <div className="text-center py-20">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertCircle className="w-8 h-8 text-red-600" />
          </div>
          <h3 className="font-display text-xl font-semibold text-white mb-2">
            Unable to Load Testimonials
          </h3>
          <p className="text-white/80 mb-6">
            We're having trouble loading client reviews. Please try again later.
          </p>
          <Button variant="outline" onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </div>
      </Section>
    )
  }

  // No testimonials state
  if (testimonials.length === 0) {
    return null
  }

  return (
    <Section background="gradient" id="testimonials">
      <AnimatedElement animation="fadeIn">
        <SectionHeader
          subtitle="Client Reviews"
          title="What Our Clients Say"
          description="Read what our satisfied clients have to say about their experience with our professional makeup services."
        />
      </AnimatedElement>

      <StaggeredContainer className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {testimonials.map((testimonial, index) => (
          <StaggeredItem key={testimonial.id}>
            <Card className="group relative h-full bg-white/80 backdrop-blur-sm border-0 hover:shadow-xl transition-all duration-300">
              {/* Quote Icon */}
              <div className="absolute top-4 right-4 text-rose-gold/20 group-hover:text-rose-gold/40 transition-colors">
                <Quote className="w-8 h-8" />
              </div>

              <CardContent className="p-6 space-y-4">
                {/* Rating */}
                <div className="flex items-center gap-1">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>

                {/* Testimonial Text */}
                <blockquote className="text-text-secondary leading-relaxed">
                  "{testimonial.message}"
                </blockquote>

                {/* Client Info */}
                <div className="flex items-center gap-4 pt-4 border-t border-gray-100">
                  <div className="relative w-12 h-12 rounded-full overflow-hidden bg-gradient-to-br from-rose-gold to-blush-pink">
                    <Image
                      src={testimonial.image || `https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face&q=80`}
                      alt={testimonial.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="flex-1">
                    <div className="font-semibold text-text-primary">
                      {testimonial.name}
                    </div>
                    {testimonial.service && (
                      <div className="text-sm text-text-secondary">
                        {testimonial.service}
                      </div>
                    )}
                    <div className="text-xs text-text-muted">
                      {formatDate(testimonial.createdAt)}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </StaggeredItem>
        ))}
      </StaggeredContainer>

      {/* Stats Section */}
      <AnimatedElement animation="slideUp" delay={0.8} className="mt-16">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div className="space-y-2">
            <div className="text-3xl md:text-4xl font-bold text-text-primary">50+</div>
            <div className="text-text-secondary">Happy Clients</div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl md:text-4xl font-bold text-text-primary">5.0</div>
            <div className="text-text-secondary">Average Rating</div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl md:text-4xl font-bold text-text-primary">100+</div>
            <div className="text-text-secondary">Makeup Sessions</div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl md:text-4xl font-bold text-text-primary">7</div>
            <div className="text-text-secondary">Cities Served</div>
          </div>
        </div>
      </AnimatedElement>
    </Section>
  )
}
