'use client'

import Image from 'next/image'
import { Star, Quote } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Section, SectionHeader } from '@/components/ui/section'
import { AnimatedElement, StaggeredContainer, StaggeredItem } from '@/components/ui/animated-element'
import { useApprovedTestimonials } from '@/hooks/use-api'
import { formatDate } from '@/lib/utils'
import { Button } from '@/components/ui/button'

// Fallback testimonials when API is not available
const fallbackTestimonials = [
  {
    id: 'fallback-1',
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    message: '<PERSON><PERSON><PERSON> did an amazing job on my wedding day! The makeup was flawless and lasted the entire day. I felt like a princess. Highly recommended for all brides!',
    rating: 5,
    service: 'Bridal Makeup',
    status: 'APPROVED' as const,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'fallback-2',
    name: '<PERSON><PERSON>',
    message: 'Perfect makeup for my engagement party. <PERSON><PERSON><PERSON> understood exactly what I wanted and delivered beyond my expectations. The team is professional and talented.',
    rating: 5,
    service: 'Party Makeup',
    status: 'APPROVED' as const,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'fallback-3',
    name: 'Kamala Thapa',
    email: '<EMAIL>',
    message: 'I had my makeup done for a traditional ceremony and it was absolutely beautiful. Anjali respects cultural traditions while adding her modern touch.',
    rating: 5,
    service: 'Traditional Makeup',
    status: 'APPROVED' as const,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'fallback-4',
    name: 'Rina Gurung',
    message: 'Amazing experience! The makeup was perfect for my photoshoot. Anjali is very professional and knows how to make you look your best on camera.',
    rating: 5,
    service: 'Photoshoot Makeup',
    status: 'APPROVED' as const,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'fallback-5',
    name: 'Maya Shrestha',
    email: '<EMAIL>',
    message: 'Excellent service and beautiful results. Anjali is not just talented but also very friendly and makes you feel comfortable throughout the process.',
    rating: 5,
    service: 'Bridal Makeup',
    status: 'APPROVED' as const,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'fallback-6',
    name: 'Sunita Poudel',
    message: 'Absolutely loved my makeup for the party! Anjali has such an artistic eye and knows exactly what suits each face. Will definitely book again!',
    rating: 5,
    service: 'Party Makeup',
    status: 'APPROVED' as const,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
]

export default function Testimonials() {
  const { data: testimonialsData } = useApprovedTestimonials(6)

  // Use API data if available, otherwise use fallback testimonials
  const testimonials = (testimonialsData?.testimonials && testimonialsData.testimonials.length > 0)
    ? testimonialsData.testimonials
    : fallbackTestimonials

  const isUsingFallback = !testimonialsData?.testimonials || testimonialsData.testimonials.length === 0

  // Always show content - either from API or fallback

  return (
    <Section background="gradient" id="testimonials">
      <AnimatedElement animation="fadeIn">
        <SectionHeader
          subtitle="Client Reviews"
          title="What Our Clients Say"
          description={isUsingFallback
            ? "Read what our satisfied clients have to say about their experience with our professional makeup services. (Sample reviews - connect to CMS for live testimonials)"
            : "Read what our satisfied clients have to say about their experience with our professional makeup services."
          }
        />
      </AnimatedElement>

      <StaggeredContainer className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {testimonials.map((testimonial, index) => (
          <StaggeredItem key={testimonial.id}>
            <Card className="group relative h-full bg-white/80 backdrop-blur-sm border-0 hover:shadow-xl transition-all duration-300">
              {/* Quote Icon */}
              <div className="absolute top-4 right-4 text-rose-gold/20 group-hover:text-rose-gold/40 transition-colors">
                <Quote className="w-8 h-8" />
              </div>

              <CardContent className="p-6 space-y-4">
                {/* Rating */}
                <div className="flex items-center gap-1">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>

                {/* Testimonial Text */}
                <blockquote className="text-text-secondary leading-relaxed">
                  "{testimonial.message}"
                </blockquote>

                {/* Client Info */}
                <div className="flex items-center gap-4 pt-4 border-t border-gray-100">
                  <div className="relative w-12 h-12 rounded-full overflow-hidden bg-gradient-to-br from-rose-gold to-blush-pink">
                    <Image
                      src={testimonial.image || `https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face&q=80`}
                      alt={testimonial.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="flex-1">
                    <div className="font-semibold text-text-primary">
                      {testimonial.name}
                    </div>
                    {testimonial.service && (
                      <div className="text-sm text-text-secondary">
                        {testimonial.service}
                      </div>
                    )}
                    <div className="text-xs text-text-muted">
                      {formatDate(testimonial.createdAt)}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </StaggeredItem>
        ))}
      </StaggeredContainer>

      {/* Stats Section */}
      <AnimatedElement animation="slideUp" delay={0.8} className="mt-16">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div className="space-y-2">
            <div className="text-3xl md:text-4xl font-bold text-text-primary">50+</div>
            <div className="text-text-secondary">Happy Clients</div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl md:text-4xl font-bold text-text-primary">5.0</div>
            <div className="text-text-secondary">Average Rating</div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl md:text-4xl font-bold text-text-primary">100+</div>
            <div className="text-text-secondary">Makeup Sessions</div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl md:text-4xl font-bold text-text-primary">7</div>
            <div className="text-text-secondary">Cities Served</div>
          </div>
        </div>
      </AnimatedElement>
    </Section>
  )
}
