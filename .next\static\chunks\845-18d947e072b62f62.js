"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[845],{4090:(e,s,a)=>{a.d(s,{X:()=>l,w:()=>i});var t=a(5155),r=a(9434);function i(e){let{children:s,className:a,id:i,background:l="default"}=e;return(0,t.jsx)("section",{id:i,className:(0,r.cn)("py-16 md:py-24",{default:"bg-white",cream:"bg-cream","soft-gray":"bg-soft-gray",gradient:"bg-gradient-to-br from-cream to-soft-gray"}[l],a),children:(0,t.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:s})})}function l(e){let{title:s,subtitle:a,description:i,centered:l=!0,className:n}=e;return(0,t.jsxs)("div",{className:(0,r.cn)("mb-12 md:mb-16",l&&"text-center",n),children:[a&&(0,t.jsx)("p",{className:"text-rose-gold-dark font-medium text-sm uppercase tracking-wide mb-2",children:a}),(0,t.jsx)("h2",{className:"font-display text-3xl md:text-4xl lg:text-5xl font-bold text-text-primary mb-4",children:s}),i&&(0,t.jsx)("p",{className:"text-text-secondary text-lg max-w-3xl mx-auto leading-relaxed",children:i})]})}},6126:(e,s,a)=>{a.d(s,{E:()=>n});var t=a(5155);a(2115);var r=a(2085),i=a(9434);let l=(0,r.F)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-rose-gold text-white shadow hover:bg-rose-gold-dark",secondary:"border-transparent bg-blush-pink text-text-primary hover:bg-blush-pink-dark",destructive:"border-transparent bg-red-500 text-white shadow hover:bg-red-600",outline:"text-text-primary border-gray-300",success:"border-transparent bg-green-500 text-white shadow hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white shadow hover:bg-yellow-600",lavender:"border-transparent bg-lavender text-text-primary hover:bg-lavender-dark"}},defaultVariants:{variant:"default"}});function n(e){let{className:s,variant:a,...r}=e;return(0,t.jsx)("div",{className:(0,i.cn)(l({variant:a}),s),...r})}},6425:(e,s,a)=>{a.d(s,{MU:()=>c,a6:()=>n,gN:()=>d});var t=a(5155),r=a(2605),i=a(9434);let l={fadeIn:{initial:{opacity:0},animate:{opacity:1}},slideUp:{initial:{opacity:0,y:50},animate:{opacity:1,y:0}},slideLeft:{initial:{opacity:0,x:50},animate:{opacity:1,x:0}},slideRight:{initial:{opacity:0,x:-50},animate:{opacity:1,x:0}},scale:{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1}},bounce:{initial:{opacity:0,y:-20},animate:{opacity:1,y:0}}};function n(e){let{children:s,className:a,animation:n="fadeIn",delay:d=0,duration:c=.6,once:o=!0}=e,m=l[n];return(0,t.jsx)(r.P.div,{className:(0,i.cn)(a),initial:m.initial,whileInView:m.animate,viewport:{once:o,margin:"-100px"},transition:{duration:c,delay:d,ease:"easeOut"},children:s})}function d(e){let{children:s,className:a,staggerDelay:l=.1}=e;return(0,t.jsx)(r.P.div,{className:(0,i.cn)(a),initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},variants:{hidden:{},visible:{transition:{staggerChildren:l}}},children:s})}function c(e){let{children:s,className:a,animation:n="slideUp"}=e,d=l[n];return(0,t.jsx)(r.P.div,{className:(0,i.cn)(a),variants:{hidden:d.initial,visible:d.animate},transition:{duration:.6,ease:"easeOut"},children:s})}},6695:(e,s,a)=>{a.d(s,{BT:()=>c,Wu:()=>o,ZB:()=>d,Zp:()=>l,aR:()=>n});var t=a(5155),r=a(2115),i=a(9434);let l=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:(0,i.cn)("rounded-xl border border-gray-200 bg-white text-text-primary shadow-sm transition-shadow hover:shadow-md",a),...r})});l.displayName="Card";let n=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",a),...r})});n.displayName="CardHeader";let d=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("h3",{ref:s,className:(0,i.cn)("font-display text-2xl font-semibold leading-none tracking-tight",a),...r})});d.displayName="CardTitle";let c=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("p",{ref:s,className:(0,i.cn)("text-sm text-text-secondary",a),...r})});c.displayName="CardDescription";let o=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",a),...r})});o.displayName="CardContent",r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",a),...r})}).displayName="CardFooter"},8292:(e,s,a)=>{a.d(s,{A:()=>j});var t=a(5155),r=a(2115),i=a(2177),l=a(221),n=a(8309),d=a(2486),c=a(9420),o=a(1366),m=a(285),x=a(9434);let h=r.forwardRef((e,s)=>{let{className:a,type:r,...i}=e;return(0,t.jsx)("input",{type:r,className:(0,x.cn)("flex h-9 w-full rounded-md border border-gray-300 bg-white px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-text-primary placeholder:text-text-muted focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-rose-gold focus-visible:border-rose-gold disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...i})});h.displayName="Input";let p=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("textarea",{className:(0,x.cn)("flex min-h-[60px] w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm placeholder:text-text-muted focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-rose-gold focus-visible:border-rose-gold disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...r})});p.displayName="Textarea";var u=a(6695),f=a(6126),g=a(1710);let b=n.Ik({name:n.Yj().min(2,"Name must be at least 2 characters"),email:n.Yj().email("Please enter a valid email address"),phone:n.Yj().min(10,"Please enter a valid phone number"),service:n.Yj().min(1,"Please select a service"),date:n.Yj().min(1,"Please select a preferred date"),message:n.Yj().min(10,"Message must be at least 10 characters")}),y=["Bridal Makeup","Party Makeup","Engagement Makeup","Traditional Makeup","Photoshoot Makeup","Makeup Lessons","Other"];function j(){let[e,s]=(0,r.useState)(!1),[a,n]=(0,r.useState)(!1),j=(0,g.Q2)(),{register:N,handleSubmit:v,formState:{errors:w},reset:k}=(0,i.mN)({resolver:(0,l.u)(b)}),M=async e=>{s(!0);try{await new Promise(e=>setTimeout(e,2e3)),n(!0),k()}catch(e){console.error("Error submitting form:",e)}finally{s(!1)}},C=(0,x.ec)(j.contact.whatsapp,j.whatsappMessage);return a?(0,t.jsx)(u.Zp,{className:"max-w-md mx-auto text-center",children:(0,t.jsxs)(u.Wu,{className:"pt-6",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)(d.A,{className:"w-8 h-8 text-green-600"})}),(0,t.jsx)("h3",{className:"font-display text-xl font-semibold text-text-primary mb-2",children:"Message Sent Successfully!"}),(0,t.jsx)("p",{className:"text-text-secondary mb-6",children:"Thank you for your inquiry. We'll get back to you within 24 hours."}),(0,t.jsx)(m.$,{onClick:()=>n(!1),variant:"outline",className:"w-full",children:"Send Another Message"})]})}):(0,t.jsxs)("div",{className:"grid lg:grid-cols-2 gap-8 max-w-4xl mx-auto",children:[(0,t.jsxs)(u.Zp,{children:[(0,t.jsxs)(u.aR,{children:[(0,t.jsx)(u.ZB,{className:"font-display text-2xl",children:"Send us a Message"}),(0,t.jsx)(u.BT,{children:"Fill out the form below and we'll get back to you as soon as possible."})]}),(0,t.jsx)(u.Wu,{children:(0,t.jsxs)("form",{onSubmit:v(M),className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{htmlFor:"name",className:"text-sm font-medium text-text-primary",children:"Full Name *"}),(0,t.jsx)(h,{id:"name",placeholder:"Your full name",...N("name"),className:w.name?"border-red-500":""}),w.name&&(0,t.jsx)("p",{className:"text-red-500 text-xs",children:w.name.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{htmlFor:"email",className:"text-sm font-medium text-text-primary",children:"Email Address *"}),(0,t.jsx)(h,{id:"email",type:"email",placeholder:"<EMAIL>",...N("email"),className:w.email?"border-red-500":""}),w.email&&(0,t.jsx)("p",{className:"text-red-500 text-xs",children:w.email.message})]})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{htmlFor:"phone",className:"text-sm font-medium text-text-primary",children:"Phone Number *"}),(0,t.jsx)(h,{id:"phone",placeholder:"+977-9800000000",...N("phone"),className:w.phone?"border-red-500":""}),w.phone&&(0,t.jsx)("p",{className:"text-red-500 text-xs",children:w.phone.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{htmlFor:"date",className:"text-sm font-medium text-text-primary",children:"Preferred Date *"}),(0,t.jsx)(h,{id:"date",type:"date",...N("date"),className:w.date?"border-red-500":""}),w.date&&(0,t.jsx)("p",{className:"text-red-500 text-xs",children:w.date.message})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{htmlFor:"service",className:"text-sm font-medium text-text-primary",children:"Service Interested In *"}),(0,t.jsxs)("select",{id:"service",...N("service"),className:"flex h-9 w-full rounded-md border border-gray-300 bg-white px-3 py-1 text-sm shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-rose-gold focus-visible:border-rose-gold ".concat(w.service?"border-red-500":""),children:[(0,t.jsx)("option",{value:"",children:"Select a service"}),y.map(e=>(0,t.jsx)("option",{value:e,children:e},e))]}),w.service&&(0,t.jsx)("p",{className:"text-red-500 text-xs",children:w.service.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{htmlFor:"message",className:"text-sm font-medium text-text-primary",children:"Message *"}),(0,t.jsx)(p,{id:"message",placeholder:"Tell us about your requirements, occasion, and any specific preferences...",rows:4,...N("message"),className:w.message?"border-red-500":""}),w.message&&(0,t.jsx)("p",{className:"text-red-500 text-xs",children:w.message.message})]}),(0,t.jsx)(m.$,{type:"submit",variant:"gradient",size:"lg",className:"w-full",disabled:e,children:e?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Sending..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.A,{className:"w-4 h-4 mr-2"}),"Send Message"]})})]})})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(u.Zp,{className:"bg-gradient-to-br from-rose-gold/10 to-blush-pink/10 border-0",children:[(0,t.jsxs)(u.aR,{children:[(0,t.jsxs)(u.ZB,{className:"font-display text-xl flex items-center gap-2",children:[(0,t.jsx)(c.A,{className:"w-5 h-5 text-rose-gold-dark"}),"Quick Contact"]}),(0,t.jsx)(u.BT,{children:"Need immediate assistance? Contact us directly via WhatsApp or phone."})]}),(0,t.jsxs)(u.Wu,{className:"space-y-4",children:[(0,t.jsx)(m.$,{asChild:!0,variant:"gradient",size:"lg",className:"w-full",children:(0,t.jsxs)("a",{href:C,target:"_blank",rel:"noopener noreferrer",children:[(0,t.jsx)(o.A,{className:"w-5 h-5 mr-2"}),"WhatsApp Now"]})}),(0,t.jsx)(m.$,{asChild:!0,variant:"outline",size:"lg",className:"w-full",children:(0,t.jsxs)("a",{href:"tel:".concat(j.contact.phone),children:[(0,t.jsx)(c.A,{className:"w-5 h-5 mr-2"}),"Call Now"]})})]})]}),(0,t.jsxs)(u.Zp,{children:[(0,t.jsx)(u.aR,{children:(0,t.jsx)(u.ZB,{className:"font-display text-xl",children:"Business Hours"})}),(0,t.jsxs)(u.Wu,{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-text-secondary",children:"Monday - Saturday"}),(0,t.jsx)(f.E,{variant:"outline",children:"9:00 AM - 6:00 PM"})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-text-secondary",children:"Sunday"}),(0,t.jsx)(f.E,{variant:"outline",children:"10:00 AM - 4:00 PM"})]}),(0,t.jsx)("div",{className:"pt-2 border-t border-gray-100",children:(0,t.jsx)("p",{className:"text-sm text-text-muted",children:"Emergency bookings available with advance notice"})})]})]})]})]})}}}]);