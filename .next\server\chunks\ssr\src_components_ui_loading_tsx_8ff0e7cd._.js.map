{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/src/components/ui/loading.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { cn } from '@/lib/utils'\n\ninterface LoadingProps {\n  size?: 'sm' | 'md' | 'lg'\n  className?: string\n}\n\nexport function Loading({ size = 'md', className }: LoadingProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12'\n  }\n\n  return (\n    <div className={cn('flex items-center justify-center', className)}>\n      <motion.div\n        className={cn(\n          'border-2 border-rose-gold/20 border-t-rose-gold rounded-full',\n          sizeClasses[size]\n        )}\n        animate={{ rotate: 360 }}\n        transition={{\n          duration: 1,\n          repeat: Infinity,\n          ease: 'linear'\n        }}\n      />\n    </div>\n  )\n}\n\nexport function PageLoading() {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-cream via-white to-blush-pink-light\">\n      <div className=\"text-center space-y-4\">\n        <motion.div\n          initial={{ scale: 0.8, opacity: 0 }}\n          animate={{ scale: 1, opacity: 1 }}\n          transition={{ duration: 0.5 }}\n          className=\"w-16 h-16 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full flex items-center justify-center mx-auto\"\n        >\n          <span className=\"text-white font-display font-bold text-2xl\">A</span>\n        </motion.div>\n        \n        <motion.div\n          initial={{ opacity: 0, y: 10 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: 0.2 }}\n        >\n          <Loading size=\"lg\" />\n        </motion.div>\n        \n        <motion.p\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ duration: 0.5, delay: 0.4 }}\n          className=\"text-text-secondary\"\n        >\n          Loading beautiful content...\n        </motion.p>\n      </div>\n    </div>\n  )\n}\n\nexport function ButtonLoading() {\n  return (\n    <motion.div\n      className=\"w-4 h-4 border-2 border-white/30 border-t-white rounded-full\"\n      animate={{ rotate: 360 }}\n      transition={{\n        duration: 1,\n        repeat: Infinity,\n        ease: 'linear'\n      }}\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAHA;;;;AAUO,SAAS,QAAQ,EAAE,OAAO,IAAI,EAAE,SAAS,EAAgB;IAC9D,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;kBACrD,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gEACA,WAAW,CAAC,KAAK;YAEnB,SAAS;gBAAE,QAAQ;YAAI;YACvB,YAAY;gBACV,UAAU;gBACV,QAAQ;gBACR,MAAM;YACR;;;;;;;;;;;AAIR;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,OAAO;wBAAK,SAAS;oBAAE;oBAClC,SAAS;wBAAE,OAAO;wBAAG,SAAS;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,8OAAC;wBAAK,WAAU;kCAA6C;;;;;;;;;;;8BAG/D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAExC,cAAA,8OAAC;wBAAQ,MAAK;;;;;;;;;;;8BAGhB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oBACP,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT;AAEO,SAAS;IACd,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,QAAQ;QAAI;QACvB,YAAY;YACV,UAAU;YACV,QAAQ;YACR,MAAM;QACR;;;;;;AAGN", "debugId": null}}]}