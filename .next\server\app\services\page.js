const CHUNK_PUBLIC_PATH = "server/app/services/page.js";
const runtime = require("../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/node_modules_8f848e63._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__d8593af1._.js");
runtime.loadChunk("server/chunks/ssr/src_app_a4430781._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_564f7ee0._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__d7fc30a8._.js");
runtime.loadChunk("server/chunks/ssr/src_app_error_tsx_9340bd74._.js");
runtime.loadChunk("server/chunks/ssr/src_96938f03._.js");
runtime.loadChunk("server/chunks/ssr/_41b2278a._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_52871ed1._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_builtin_unauthorized_a3bc0a28.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_builtin_global-error_af93ecd6.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_bfbe28bf._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__38537a4d._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/services/page/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/services/page { GLOBAL_ERROR_MODULE => \"[project]/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/src/app/error.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/src/app/loading.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/src/app/not-found.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_7 => \"[project]/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_8 => \"[project]/src/app/services/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/services/page { GLOBAL_ERROR_MODULE => \"[project]/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/src/app/error.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/src/app/loading.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/src/app/not-found.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_7 => \"[project]/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_8 => \"[project]/src/app/services/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
