"use strict";exports.id=480,exports.ids=[480],exports.modules={18419:(a,b,c)=>{c.d(b,{MU:()=>j,a6:()=>h,gN:()=>i});var d=c(60687),e=c(51743),f=c(4780);let g={fadeIn:{initial:{opacity:0},animate:{opacity:1}},slideUp:{initial:{opacity:0,y:50},animate:{opacity:1,y:0}},slideLeft:{initial:{opacity:0,x:50},animate:{opacity:1,x:0}},slideRight:{initial:{opacity:0,x:-50},animate:{opacity:1,x:0}},scale:{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1}},bounce:{initial:{opacity:0,y:-20},animate:{opacity:1,y:0}}};function h({children:a,className:b,animation:c="fadeIn",delay:h=0,duration:i=.6,once:j=!0}){let k=g[c];return(0,d.jsx)(e.P.div,{className:(0,f.cn)(b),initial:k.initial,whileInView:k.animate,viewport:{once:j,margin:"-100px"},transition:{duration:i,delay:h,ease:"easeOut"},children:a})}function i({children:a,className:b,staggerDelay:c=.1}){return(0,d.jsx)(e.P.div,{className:(0,f.cn)(b),initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},variants:{hidden:{},visible:{transition:{staggerChildren:c}}},children:a})}function j({children:a,className:b,animation:c="slideUp"}){let h=g[c];return(0,d.jsx)(e.P.div,{className:(0,f.cn)(b),variants:{hidden:h.initial,visible:h.animate},transition:{duration:.6,ease:"easeOut"},children:a})}},34473:(a,b,c)=>{c.d(b,{A:()=>u});var d=c(60687),e=c(43210),f=c(27605),g=c(63442),h=c(37566),i=c(27900),j=c(48340),k=c(33872),l=c(29523),m=c(4780);let n=e.forwardRef(({className:a,type:b,...c},e)=>(0,d.jsx)("input",{type:b,className:(0,m.cn)("flex h-9 w-full rounded-md border border-gray-300 bg-white px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-text-primary placeholder:text-text-muted focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-rose-gold focus-visible:border-rose-gold disabled:cursor-not-allowed disabled:opacity-50",a),ref:e,...c}));n.displayName="Input";let o=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("textarea",{className:(0,m.cn)("flex min-h-[60px] w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm placeholder:text-text-muted focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-rose-gold focus-visible:border-rose-gold disabled:cursor-not-allowed disabled:opacity-50",a),ref:c,...b}));o.displayName="Textarea";var p=c(44493),q=c(96834),r=c(53462);let s=h.Ik({name:h.Yj().min(2,"Name must be at least 2 characters"),email:h.Yj().email("Please enter a valid email address"),phone:h.Yj().min(10,"Please enter a valid phone number"),service:h.Yj().min(1,"Please select a service"),date:h.Yj().min(1,"Please select a preferred date"),message:h.Yj().min(10,"Message must be at least 10 characters")}),t=["Bridal Makeup","Party Makeup","Engagement Makeup","Traditional Makeup","Photoshoot Makeup","Makeup Lessons","Other"];function u(){let[a,b]=(0,e.useState)(!1),[c,h]=(0,e.useState)(!1),u=(0,r.Q2)(),{register:v,handleSubmit:w,formState:{errors:x},reset:y}=(0,f.mN)({resolver:(0,g.u)(s)}),z=async a=>{b(!0);try{await new Promise(a=>setTimeout(a,2e3)),h(!0),y()}catch(a){console.error("Error submitting form:",a)}finally{b(!1)}},A=(0,m.ec)(u.contact.whatsapp,u.whatsappMessage);return c?(0,d.jsx)(p.Zp,{className:"max-w-md mx-auto text-center",children:(0,d.jsxs)(p.Wu,{className:"pt-6",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(i.A,{className:"w-8 h-8 text-green-600"})}),(0,d.jsx)("h3",{className:"font-display text-xl font-semibold text-text-primary mb-2",children:"Message Sent Successfully!"}),(0,d.jsx)("p",{className:"text-text-secondary mb-6",children:"Thank you for your inquiry. We'll get back to you within 24 hours."}),(0,d.jsx)(l.$,{onClick:()=>h(!1),variant:"outline",className:"w-full",children:"Send Another Message"})]})}):(0,d.jsxs)("div",{className:"grid lg:grid-cols-2 gap-8 max-w-4xl mx-auto",children:[(0,d.jsxs)(p.Zp,{children:[(0,d.jsxs)(p.aR,{children:[(0,d.jsx)(p.ZB,{className:"font-display text-2xl",children:"Send us a Message"}),(0,d.jsx)(p.BT,{children:"Fill out the form below and we'll get back to you as soon as possible."})]}),(0,d.jsx)(p.Wu,{children:(0,d.jsxs)("form",{onSubmit:w(z),className:"space-y-4",children:[(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("label",{htmlFor:"name",className:"text-sm font-medium text-text-primary",children:"Full Name *"}),(0,d.jsx)(n,{id:"name",placeholder:"Your full name",...v("name"),className:x.name?"border-red-500":""}),x.name&&(0,d.jsx)("p",{className:"text-red-500 text-xs",children:x.name.message})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("label",{htmlFor:"email",className:"text-sm font-medium text-text-primary",children:"Email Address *"}),(0,d.jsx)(n,{id:"email",type:"email",placeholder:"<EMAIL>",...v("email"),className:x.email?"border-red-500":""}),x.email&&(0,d.jsx)("p",{className:"text-red-500 text-xs",children:x.email.message})]})]}),(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("label",{htmlFor:"phone",className:"text-sm font-medium text-text-primary",children:"Phone Number *"}),(0,d.jsx)(n,{id:"phone",placeholder:"+977-9800000000",...v("phone"),className:x.phone?"border-red-500":""}),x.phone&&(0,d.jsx)("p",{className:"text-red-500 text-xs",children:x.phone.message})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("label",{htmlFor:"date",className:"text-sm font-medium text-text-primary",children:"Preferred Date *"}),(0,d.jsx)(n,{id:"date",type:"date",...v("date"),className:x.date?"border-red-500":""}),x.date&&(0,d.jsx)("p",{className:"text-red-500 text-xs",children:x.date.message})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("label",{htmlFor:"service",className:"text-sm font-medium text-text-primary",children:"Service Interested In *"}),(0,d.jsxs)("select",{id:"service",...v("service"),className:`flex h-9 w-full rounded-md border border-gray-300 bg-white px-3 py-1 text-sm shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-rose-gold focus-visible:border-rose-gold ${x.service?"border-red-500":""}`,children:[(0,d.jsx)("option",{value:"",children:"Select a service"}),t.map(a=>(0,d.jsx)("option",{value:a,children:a},a))]}),x.service&&(0,d.jsx)("p",{className:"text-red-500 text-xs",children:x.service.message})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("label",{htmlFor:"message",className:"text-sm font-medium text-text-primary",children:"Message *"}),(0,d.jsx)(o,{id:"message",placeholder:"Tell us about your requirements, occasion, and any specific preferences...",rows:4,...v("message"),className:x.message?"border-red-500":""}),x.message&&(0,d.jsx)("p",{className:"text-red-500 text-xs",children:x.message.message})]}),(0,d.jsx)(l.$,{type:"submit",variant:"gradient",size:"lg",className:"w-full",disabled:a,children:a?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Sending..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"Send Message"]})})]})})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)(p.Zp,{className:"bg-gradient-to-br from-rose-gold/10 to-blush-pink/10 border-0",children:[(0,d.jsxs)(p.aR,{children:[(0,d.jsxs)(p.ZB,{className:"font-display text-xl flex items-center gap-2",children:[(0,d.jsx)(j.A,{className:"w-5 h-5 text-rose-gold-dark"}),"Quick Contact"]}),(0,d.jsx)(p.BT,{children:"Need immediate assistance? Contact us directly via WhatsApp or phone."})]}),(0,d.jsxs)(p.Wu,{className:"space-y-4",children:[(0,d.jsx)(l.$,{asChild:!0,variant:"gradient",size:"lg",className:"w-full",children:(0,d.jsxs)("a",{href:A,target:"_blank",rel:"noopener noreferrer",children:[(0,d.jsx)(k.A,{className:"w-5 h-5 mr-2"}),"WhatsApp Now"]})}),(0,d.jsx)(l.$,{asChild:!0,variant:"outline",size:"lg",className:"w-full",children:(0,d.jsxs)("a",{href:`tel:${u.contact.phone}`,children:[(0,d.jsx)(j.A,{className:"w-5 h-5 mr-2"}),"Call Now"]})})]})]}),(0,d.jsxs)(p.Zp,{children:[(0,d.jsx)(p.aR,{children:(0,d.jsx)(p.ZB,{className:"font-display text-xl",children:"Business Hours"})}),(0,d.jsxs)(p.Wu,{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-text-secondary",children:"Monday - Saturday"}),(0,d.jsx)(q.E,{variant:"outline",children:"9:00 AM - 6:00 PM"})]}),(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-text-secondary",children:"Sunday"}),(0,d.jsx)(q.E,{variant:"outline",children:"10:00 AM - 4:00 PM"})]}),(0,d.jsx)("div",{className:"pt-2 border-t border-gray-100",children:(0,d.jsx)("p",{className:"text-sm text-text-muted",children:"Emergency bookings available with advance notice"})})]})]})]})]})}},71134:(a,b,c)=>{c.d(b,{X:()=>g,w:()=>f});var d=c(60687),e=c(4780);function f({children:a,className:b,id:c,background:f="default"}){return(0,d.jsx)("section",{id:c,className:(0,e.cn)("py-16 md:py-24",{default:"bg-white",cream:"bg-cream","soft-gray":"bg-soft-gray",gradient:"bg-gradient-to-br from-cream to-soft-gray"}[f],b),children:(0,d.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:a})})}function g({title:a,subtitle:b,description:c,centered:f=!0,className:g}){return(0,d.jsxs)("div",{className:(0,e.cn)("mb-12 md:mb-16",f&&"text-center",g),children:[b&&(0,d.jsx)("p",{className:"text-rose-gold-dark font-medium text-sm uppercase tracking-wide mb-2",children:b}),(0,d.jsx)("h2",{className:"font-display text-3xl md:text-4xl lg:text-5xl font-bold text-text-primary mb-4",children:a}),c&&(0,d.jsx)("p",{className:"text-text-secondary text-lg max-w-3xl mx-auto leading-relaxed",children:c})]})}},96834:(a,b,c)=>{c.d(b,{E:()=>h});var d=c(60687);c(43210);var e=c(24224),f=c(4780);let g=(0,e.F)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-rose-gold text-white shadow hover:bg-rose-gold-dark",secondary:"border-transparent bg-blush-pink text-text-primary hover:bg-blush-pink-dark",destructive:"border-transparent bg-red-500 text-white shadow hover:bg-red-600",outline:"text-text-primary border-gray-300",success:"border-transparent bg-green-500 text-white shadow hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white shadow hover:bg-yellow-600",lavender:"border-transparent bg-lavender text-text-primary hover:bg-lavender-dark"}},defaultVariants:{variant:"default"}});function h({className:a,variant:b,...c}){return(0,d.jsx)("div",{className:(0,f.cn)(g({variant:b}),a),...c})}}};