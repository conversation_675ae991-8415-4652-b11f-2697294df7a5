'use client'

import Link from 'next/link'
import Image from 'next/image'
import { ArrowRight, Eye, Loader2, AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Section, SectionHeader } from '@/components/ui/section'
import { AnimatedElement, StaggeredContainer, StaggeredItem } from '@/components/ui/animated-element'
import { useFeaturedGallery } from '@/hooks/use-api'

export default function GalleryShowcase() {
  const { data: galleryData, isLoading, error } = useFeaturedGallery(8)

  const featuredItems = galleryData?.gallery || []

  // Loading state
  if (isLoading) {
    return (
      <Section id="gallery">
        <AnimatedElement animation="fadeIn">
          <SectionHeader
            subtitle="Portfolio"
            title="Our Work Gallery"
            description="Explore our stunning portfolio showcasing diverse makeup artistry across various occasions and styles."
          />
        </AnimatedElement>
        <div className="flex justify-center items-center py-20">
          <Loader2 className="w-8 h-8 animate-spin text-rose-gold" />
          <span className="ml-3 text-text-secondary">Loading gallery...</span>
        </div>
      </Section>
    )
  }

  // Error state
  if (error) {
    return (
      <Section id="gallery">
        <AnimatedElement animation="fadeIn">
          <SectionHeader
            subtitle="Portfolio"
            title="Our Work Gallery"
            description="Explore our stunning portfolio showcasing diverse makeup artistry across various occasions and styles."
          />
        </AnimatedElement>
        <div className="text-center py-20">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertCircle className="w-8 h-8 text-red-600" />
          </div>
          <h3 className="font-display text-xl font-semibold text-text-primary mb-2">
            Unable to Load Gallery
          </h3>
          <p className="text-text-secondary mb-6">
            We're having trouble loading our portfolio. Please try again later.
          </p>
          <Button variant="outline" onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </div>
      </Section>
    )
  }

  // No gallery items state
  if (featuredItems.length === 0) {
    return null
  }

  return (
    <Section id="gallery">
      <AnimatedElement animation="fadeIn">
        <SectionHeader
          subtitle="Our Work"
          title="Portfolio Gallery"
          description="Explore our stunning portfolio showcasing diverse makeup styles and transformations for various occasions."
        />
      </AnimatedElement>

      <StaggeredContainer className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
        {featuredItems.map((item, index) => (
          <StaggeredItem key={item.id}>
            <div className="group relative aspect-square overflow-hidden rounded-xl bg-gradient-to-br from-rose-gold/10 to-blush-pink/10">
              <Image
                src={item.image}
                alt={item.title}
                fill
                className="object-cover group-hover:scale-110 transition-transform duration-500"
              />

              {/* Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                  {item.category && (
                    <Badge variant="secondary" className="mb-2 text-xs">
                      {item.category}
                    </Badge>
                  )}
                  <h3 className="font-display text-lg font-semibold mb-1">
                    {item.title}
                  </h3>
                  {item.description && (
                    <p className="text-sm text-white/80">
                      {item.description}
                    </p>
                  )}
                </div>
              </div>

              {/* View Icon */}
              <div className="absolute top-4 right-4 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Eye className="w-5 h-5 text-white" />
              </div>

              {/* Tags */}
              <div className="absolute top-4 left-4 flex flex-wrap gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                {item.tags.slice(0, 2).map((tag) => (
                  <Badge key={tag} variant="secondary" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          </StaggeredItem>
        ))}
      </StaggeredContainer>

      {/* Gallery Grid Layout for larger screens */}
      <div className="hidden lg:block mb-12">
        <StaggeredContainer className="grid grid-cols-4 grid-rows-2 gap-4 h-96">
          {featuredItems.slice(0, 6).map((item, index) => {
            const spanClasses = [
              'col-span-2 row-span-2', // Large
              'col-span-1 row-span-1', // Small
              'col-span-1 row-span-1', // Small
              'col-span-1 row-span-2', // Tall
              'col-span-1 row-span-1', // Small
              'col-span-1 row-span-1', // Small
            ]
            
            return (
              <StaggeredItem key={`grid-${item.id}`}>
                <div className={`group relative overflow-hidden rounded-xl bg-gradient-to-br from-rose-gold/10 to-blush-pink/10 ${spanClasses[index]}`}>
                  <Image
                    src={`https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=400&fit=crop&crop=face&q=80`}
                    alt={item.title}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  
                  {/* Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                      <Badge variant="secondary" className="mb-2 text-xs">
                        {item.category}
                      </Badge>
                      <h3 className="font-display text-sm font-semibold mb-1">
                        {item.title}
                      </h3>
                    </div>
                  </div>

                  {/* View Icon */}
                  <div className="absolute top-3 right-3 w-8 h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <Eye className="w-4 h-4 text-white" />
                  </div>
                </div>
              </StaggeredItem>
            )
          })}
        </StaggeredContainer>
      </div>

      <AnimatedElement animation="slideUp" delay={0.6} className="text-center">
        <Button asChild variant="gradient" size="lg">
          <Link href="/portfolio">
            View Full Portfolio
            <ArrowRight className="w-5 h-5 ml-2" />
          </Link>
        </Button>
      </AnimatedElement>
    </Section>
  )
}
