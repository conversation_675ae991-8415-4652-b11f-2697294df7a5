{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/src/components/sections/portfolio-hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/portfolio-hero.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/portfolio-hero.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8S,GAC3U,4EACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/src/components/sections/portfolio-hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/portfolio-hero.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/portfolio-hero.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0R,GACvT,wDACA", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/src/components/sections/portfolio-gallery.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/portfolio-gallery.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/portfolio-gallery.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiT,GAC9U,+EACA", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/src/components/sections/portfolio-gallery.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/portfolio-gallery.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/portfolio-gallery.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/src/components/sections/portfolio-cta.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/portfolio-cta.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/portfolio-cta.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6S,GAC1U,2EACA", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/src/components/sections/portfolio-cta.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/portfolio-cta.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/portfolio-cta.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyR,GACtT,uDACA", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/src/app/portfolio/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\nimport PortfolioHero from '@/components/sections/portfolio-hero'\nimport PortfolioGallery from '@/components/sections/portfolio-gallery'\nimport PortfolioCTA from '@/components/sections/portfolio-cta'\nimport { getSiteConfig } from '@/lib/data'\n\nconst siteConfig = getSiteConfig()\n\nexport const metadata: Metadata = {\n  title: `Portfolio & Gallery | ${siteConfig.site.name}`,\n  description: 'Browse our stunning portfolio of makeup transformations. See our work in bridal, party, traditional, and photoshoot makeup across Nepal.',\n  openGraph: {\n    title: `Portfolio & Gallery | ${siteConfig.site.name}`,\n    description: 'Browse our stunning portfolio of makeup transformations. See our work in bridal, party, traditional, and photoshoot makeup across Nepal.',\n    url: `${siteConfig.site.url}/portfolio`,\n  },\n}\n\nexport default function PortfolioPage() {\n  return (\n    <>\n      <PortfolioHero />\n      <PortfolioGallery />\n      <PortfolioCTA />\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AAAA;;;;;;AAEA,MAAM,aAAa,CAAA,GAAA,kIAAA,CAAA,gBAAa,AAAD;AAExB,MAAM,WAAqB;IAChC,OAAO,CAAC,sBAAsB,EAAE,WAAW,IAAI,CAAC,IAAI,EAAE;IACtD,aAAa;IACb,WAAW;QACT,OAAO,CAAC,sBAAsB,EAAE,WAAW,IAAI,CAAC,IAAI,EAAE;QACtD,aAAa;QACb,KAAK,GAAG,WAAW,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;IACzC;AACF;AAEe,SAAS;IACtB,qBACE;;0BACE,8OAAC,mJAAA,CAAA,UAAa;;;;;0BACd,8OAAC,sJAAA,CAAA,UAAgB;;;;;0BACjB,8OAAC,kJAAA,CAAA,UAAY;;;;;;;AAGnB", "debugId": null}}]}